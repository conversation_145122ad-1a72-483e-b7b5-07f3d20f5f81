# Example CI/CD workflow for controlled deployments
name: Deploy to Production

on:
  push:
    tags:
      - 'v*'  # Only deploy on version tags
  workflow_dispatch:  # Allow manual deployment

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci  # Uses lock file for consistent installs
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to staging first
      if: contains(github.ref, 'beta')
      run: echo "Deploy to staging"
    
    - name: Deploy to production
      if: startsWith(github.ref, 'refs/tags/v') && !contains(github.ref, 'beta')
      run: echo "Deploy to production"
