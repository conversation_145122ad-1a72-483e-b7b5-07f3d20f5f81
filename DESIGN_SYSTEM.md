# RainbowPaws Design System

## Overview

This document outlines the design system and component guidelines for the RainbowPaws application. It provides standards for consistent UI/UX across the platform.

## Color System

### Primary Colors
- **Primary Green**: `#1B4D3E` (var(--primary-green))
- **Primary Green Light**: `#2C7A62` (var(--primary-green-light))
- **Primary Green Dark**: `#153C31` (var(--primary-green-dark))
- **Primary Green Hover**: `#1F5A49` (var(--primary-green-hover))

### Semantic Colors
- **Success**: `#10b981` (Emerald 500)
- **Warning**: `#f59e0b` (Amber 500)
- **Error**: `#ef4444` (Red 500)
- **Info**: `#3b82f6` (Blue 500)

### Neutral Colors
- **Text Primary**: `#1B4D3E` (var(--text-primary))
- **Text Secondary**: `#4A5568` (var(--text-secondary))
- **Text Light**: `#718096` (var(--text-light))
- **Background Light**: `#F8FAF9` (var(--background-light))

## Typography

### Font Families
- **Sans Serif**: Inter (var(--font-sans))
- **Serif**: Playfair Display (var(--font-serif))

### Font Scales
- **xs**: 12px
- **sm**: 14px
- **base**: 16px
- **lg**: 18px
- **xl**: 20px
- **2xl**: 24px
- **3xl**: 30px
- **4xl**: 36px

## Component Guidelines

### Button Component

#### Usage
```tsx
import { Button } from '@/components/ui/Button';

<Button variant="primary" size="md">
  Click me
</Button>
```

#### Variants
- **primary**: Main action buttons (green background)
- **secondary**: Secondary actions (white background)
- **outline**: Outlined buttons (green border)
- **ghost**: Minimal buttons (transparent background)
- **link**: Text-only buttons
- **danger**: Destructive actions (red background)

#### Sizes
- **xs**: Extra small (text-xs, px-2, py-1)
- **sm**: Small (text-sm, px-3, py-1.5)
- **md**: Medium (text-sm, px-4, py-2) - Default
- **lg**: Large (text-base, px-6, py-3)
- **xl**: Extra large (text-lg, px-8, py-4)

#### Accessibility
- Always includes proper ARIA attributes
- Loading state with screen reader support
- Focus management with visible focus rings

### Input Component

#### Usage
```tsx
import { Input } from '@/components/ui/Input';

<Input
  label="Email"
  type="email"
  placeholder="Enter your email"
  required
  error={errorMessage}
/>
```

#### Features
- Built-in label and error handling
- Icon support (left and right)
- Responsive sizing
- Accessibility compliant

### Modal Component

#### Usage
```tsx
import { Modal } from '@/components/ui/Modal';

<Modal
  isOpen={isOpen}
  onClose={onClose}
  title="Modal Title"
  size="medium"
>
  Modal content
</Modal>
```

#### Sizes
- **small**: max-w-md
- **medium**: max-w-lg (Default)
- **large**: max-w-2xl
- **xlarge**: max-w-4xl
- **2xl**: max-w-5xl
- **fullscreen**: 95vw x 95vh

#### Features
- Keyboard navigation (ESC to close)
- Focus management
- Backdrop click to close
- Responsive design
- Accessibility compliant

## Loading States

### Components Available
- **Spinner**: Basic loading spinner
- **PageLoader**: Full page loading with optional logo
- **SectionLoader**: Loading for specific sections
- **LoadingOverlay**: Overlay loading state

### Usage Guidelines
- Use PageLoader for initial page loads
- Use SectionLoader for content sections
- Use LoadingOverlay for form submissions
- Always provide meaningful loading messages

## Error Handling

### Guidelines
- Use inline errors for form validation
- Avoid showing both toast and inline errors simultaneously
- Provide clear, actionable error messages
- Use proper ARIA attributes for screen readers

### Error Message Patterns
- **Validation**: "Field name is required"
- **Network**: "Connection error. Please try again."
- **Server**: "Server error. Please try again later."
- **Authentication**: "Invalid credentials. Please try again."

## Accessibility Standards

### Requirements
- All interactive elements must be keyboard accessible
- Proper ARIA labels and descriptions
- Color contrast ratio of at least 4.5:1
- Focus indicators on all interactive elements
- Screen reader support for dynamic content

### Implementation
- Use semantic HTML elements
- Provide alternative text for images
- Use proper heading hierarchy
- Include skip navigation links
- Test with screen readers

## Mobile Responsiveness

### Breakpoints
- **sm**: 640px and up
- **md**: 768px and up
- **lg**: 1024px and up
- **xl**: 1280px and up

### Guidelines
- Mobile-first design approach
- Touch-friendly interactive elements (min 44px)
- Responsive typography and spacing
- Optimized navigation for mobile devices

## CSS Custom Properties

### Usage
Always provide fallback values for CSS custom properties:

```css
/* Good */
color: var(--primary-green, #10b981);

/* Bad */
color: var(--primary-green);
```

### Available Properties
- `--primary-green`: #1B4D3E
- `--primary-green-hover`: #1F5A49
- `--primary-green-bg`: rgba(27, 77, 62, 0.1)
- `--text-primary`: #1B4D3E
- `--text-secondary`: #4A5568

## Development Guidelines

### File Organization
- Components in `/src/components/`
- UI components in `/src/components/ui/`
- Utilities in `/src/utils/`
- Types in `/src/types/`

### Naming Conventions
- PascalCase for component files
- camelCase for utility functions
- kebab-case for CSS classes
- SCREAMING_SNAKE_CASE for constants

### Code Quality
- Use TypeScript for type safety
- Follow ESLint and Prettier configurations
- Write meaningful component documentation
- Include proper error boundaries

## Testing Guidelines

### Component Testing
- Test user interactions
- Verify accessibility features
- Test responsive behavior
- Validate error states

### Best Practices
- Write tests that reflect user behavior
- Use semantic queries in tests
- Test keyboard navigation
- Verify ARIA attributes

## Performance Considerations

### Optimization
- Use React.memo for expensive components
- Implement proper loading states
- Optimize images and assets
- Minimize bundle size

### Monitoring
- Track Core Web Vitals
- Monitor loading performance
- Measure user interaction metrics
- Regular accessibility audits
